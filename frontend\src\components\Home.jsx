import { useState, useEffect, use } from 'react';
import {
  Container,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Button,
  Box,
  Chip
} from '@mui/material';
import axios from 'axios';

const Home = () => {
  const [blogPosts, setBlogPosts] = useState([]);
  const navigate = useNavigate();


  const fetchBlogs = async () => {
    try {
      const response = await axios.get('http://localhost:3001/get');
      setBlogPosts(response.data);
    } catch (err) {
      console.error('Error fetching blogs:', err);
    }
  };

  useEffect(() => {
    fetchBlogs();
  }, []);

  const handleDelete = async (id) => {
    try {
      await axios.delete(`http://localhost:3001/delete/${id}`);
      fetchBlogs();
      alert('Blog post deleted successfully');
    } catch (err) {
      console.error('Error deleting blog:', err);
    }
  };

  const handleUpdate = (id) => {
    navigate(`/add`, { state: { id } });
  };

  const getCategoryFromTitle = (title) => {
    const lowerTitle = title.toLowerCase();
    if (lowerTitle.includes('travel')) return 'Travel';
    if (lowerTitle.includes('art')) return 'Art';
    if (lowerTitle.includes('food')) return 'Food';
    return 'Blog';
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {blogPosts.length === 0 ? (
        <Box sx={{ textAlign: 'center', mt: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No blog posts found. Add some posts to get started!
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={4}>
          {blogPosts.map((post) => (
            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={post._id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  borderRadius: 2,
                  boxShadow: 3,
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 6
                  }
                }}
              >
                <CardMedia
                  component="img"
                  height="200"
                  image={post.img_url}
                  alt={post.title}
                  sx={{ objectFit: 'cover' }}
                />
                <CardContent sx={{ flexGrow: 1, p: 2 }}>
                  <Chip
                    label={getCategoryFromTitle(post.title)}
                    size="small"
                    sx={{
                      backgroundColor: '#9c27b0',
                      color: 'white',
                      fontWeight: 'bold',
                      mb: 1
                    }}
                  />
                  <Typography
                    variant="h6"
                    component="h2"
                    sx={{
                      fontWeight: 'bold',
                      mb: 1,
                      color: '#333'
                    }}
                  >
                    {post.title}
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      mb: 2,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}
                  >
                    {post.content}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, mt: 'auto' }}>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{
                        backgroundColor: '#9c27b0',
                        color: 'white',
                        fontWeight: 'bold',
                        textTransform: 'uppercase',
                        fontSize: '0.75rem',
                        '&:hover': {
                          backgroundColor: '#7b1fa2'
                        }
                      }}
                      onClick={() => {handleDelete(post._id)}}
                    >
                      Delete
                    </Button>
                    <Button
                      variant="contained"
                      size="small"
                      sx={{
                        backgroundColor: '#9c27b0',
                        color: 'white',
                        fontWeight: 'bold',
                        textTransform: 'uppercase',
                        fontSize: '0.75rem',
                        '&:hover': {
                          backgroundColor: '#7b1fa2'
                        }
                      }} onClick={() => {handleUpdate(post._id)}}
                    >
                      Update
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}
    </Container>
  );
};

export default Home;